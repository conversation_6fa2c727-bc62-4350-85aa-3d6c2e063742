// +gocover:ignore:file ignore for factory implementation
package reconcilers

import (
	"net/url"
	"time"

	msiprotos "go.goms.io/aks/rp/msi/protos"
	hcpMC "go.goms.io/aks/rp/protos/hcp/types/managedcluster/type/v1"
	asyncgoalresolvers "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/goalresolvers"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/goalresolvers/routetable"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/goalresolvers/vnet"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/rawgoalretriever"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/goalresolvers"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool/vm"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool/vmss"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/basiclb"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/natgateway"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/outboundip"
	outbound_reconciler "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/reconciler"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/slb"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/privatecluster"
	routetable_reconciler "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/routetable"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/securitygroup"
	nsg_reconciler "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/securitygroup"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/subnet"
	vnet_reconciler "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/vnet"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/rphcpclient"
	"go.goms.io/aks/rp/swift/subnethandlerclient"
	"go.goms.io/aks/rp/toolkit/azureclients/applicationsecuritygroupclient"
	"go.goms.io/aks/rp/toolkit/azureclients/blobclient"
	"go.goms.io/aks/rp/toolkit/azureclients/denyassignmentclient"
	"go.goms.io/aks/rp/toolkit/azureclients/dnsclient"
	"go.goms.io/aks/rp/toolkit/azureclients/loadbalancer"
	"go.goms.io/aks/rp/toolkit/azureclients/natgatewayclient"
	"go.goms.io/aks/rp/toolkit/azureclients/networkinterfaceclient"
	"go.goms.io/aks/rp/toolkit/azureclients/privatednsclient"
	"go.goms.io/aks/rp/toolkit/azureclients/privateendpointclient"
	"go.goms.io/aks/rp/toolkit/azureclients/privatelinkserviceclient"
	"go.goms.io/aks/rp/toolkit/azureclients/publicipaddress"
	"go.goms.io/aks/rp/toolkit/azureclients/publicipprefix"
	"go.goms.io/aks/rp/toolkit/azureclients/resourceprovider"
	"go.goms.io/aks/rp/toolkit/azureclients/routetableclient"
	"go.goms.io/aks/rp/toolkit/azureclients/securitygroupclient"
	"go.goms.io/aks/rp/toolkit/azureclients/subnetclient"
	"go.goms.io/aks/rp/toolkit/azureclients/userassignedidentityclient"
	"go.goms.io/aks/rp/toolkit/azureclients/vmclient"
	"go.goms.io/aks/rp/toolkit/azureclients/vmssclient"
	"go.goms.io/aks/rp/toolkit/azureclients/vnetclient"
	"go.goms.io/aks/rp/toolkit/log"
	"go.goms.io/aks/rp/toolkit/retry"
)

type factory struct{}

func NewFactory() FactoryInterface {
	return &factory{}
}

func (f *factory) NewResourceProviderRegistrationReconciler(
	settings retry.Settings,
	rpClient resourceprovider.Interface,
	rpTrack2Client azureresources.ResourceProvidersInterface,
	rpGoalResolver goalresolvers.ResourceProviderRegistrationInterface,
	opts ...ResourceProviderRegistrationReconcilerOption,
) Reconciler {
	r := newResourceProviderRegistrationReconciler(rpClient, rpTrack2Client, rpGoalResolver, opts...)
	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewDNSReconciler(
	settings retry.Settings,
	dnsClient dnsclient.Interface,
	dnsRecordSetTrack2Client azureresources.DNSRecordSetInterface,
	dnsGoalResolver goalresolvers.DNSInterface,
	mcResourceId,
	ccpId string,
	opts ...DNSReconcilerOption,
) Reconciler {
	r := newDNSReconciler(dnsClient, dnsRecordSetTrack2Client, dnsGoalResolver, mcResourceId, ccpId, opts...)
	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewResourceGroupReconciler(
	settings retry.Settings,
	rgClient azureresources.ResourceGroupInterface,
	rgGoalResolver goalresolvers.ResourceGroupInterface,
	hasAgentPoolWithDedicatedHosts bool,
	flags ResourceGroupReconcilerFlags) Reconciler {
	r := &resourceGroupReconciler{
		resourceGroupTrack2Client:      rgClient,
		resourceGroupGoalResolver:      rgGoalResolver,
		hasAgentPoolWithDedicatedHosts: hasAgentPoolWithDedicatedHosts,
		flags:                          flags,
	}

	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewDenyAssignmentReconciler(
	settings retry.Settings,
	daClient denyassignmentclient.Interface,
	daGoalResolver goalresolvers.DenyAssignmentInterface) Reconciler {
	r := &denyAssignmentReconciler{
		denyAssignmentClient:       daClient,
		denyAssignmentGoalResolver: daGoalResolver,
	}
	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewOutboundReconciler(
	settings retry.Settings,
	resourceProviderClient resourceprovider.Interface,
	resourceProviderTrack2Client azureresources.ResourceProvidersInterface,
	publicIPAddressClient publicipaddress.Interface,
	publicIPAddressTrack2Client azureresources.PublicIPAddressInterface,
	publicIPPrefixClient publicipprefix.Interface,
	publicIPPrefixTrack2Client azureresources.PublicIPPrefixesInterface,
	loadbalancerClient loadbalancer.Interface,
	subnetClient subnetclient.Interface,
	subnetTrack2Client azureresources.SubnetInterface,
	natGatewayClient natgatewayclient.Interface,
	natGatewayTrack2Client azureresources.NatGatewaysInterface,
	vmssClient vmssclient.Interface,
	vmClient vmclient.Interface,
	vmClientTrack2 azureresources.VirtualMachineInterface,
	nicClient networkinterfaceclient.Interface,
	nicClientTrack2 azureresources.NetworkInterfacesInterface,
	goalResolver goalresolvers.OutboundInterface,
	agentPoolsRetriever rawgoalretriever.AgentPoolsInterface,
	//cleanUpMode remove all stale outbound resources and configurations when outbound type is changed in managed cluster
	// should be called after the new outbound type has been set and applied to the cluster
	cleanUpMode bool,
	opt outbound_reconciler.OutboundReconcilerFlags,
) Reconciler {
	vmssReconciler := vmss.New(vmssClient)
	vmReconciler := vm.New(vmClient, nicClient)
	vmReconcilerTrack2 := vm.NewV2(opt, vmClientTrack2, nicClientTrack2)
	outboundIPReconciler := outboundip.New(resourceProviderClient, resourceProviderTrack2Client, publicIPAddressClient, publicIPAddressTrack2Client, publicIPPrefixClient, publicIPPrefixTrack2Client, opt)
	slbReconciler := slb.New(loadbalancerClient, vmssReconciler, vmReconciler, vmReconcilerTrack2, publicIPAddressClient, publicIPAddressTrack2Client, agentPoolsRetriever, opt)
	basicLBReconciler := basiclb.New(loadbalancerClient, vmssReconciler, vmReconcilerTrack2, opt)
	natgatewayReconciler := natgateway.New(subnetClient, subnetTrack2Client, natGatewayClient, natGatewayTrack2Client, opt)
	r := &outbound_reconciler.OutboundReconciler{
		GoalResolver:                 goalResolver,
		SlbOutboundReconciler:        slbReconciler,
		NatgatewayOutboundReconciler: natgatewayReconciler,
		OutboundIPReconciler:         outboundIPReconciler,
		BasicLBReconciler:            basicLBReconciler,
		CleanUpMode:                  cleanUpMode,
	}

	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewRouteTableReconciler(
	settings retry.Settings,
	routeTableClient routetableclient.Interface,
	routeTableTrack2Client azureresources.RouteTablesInterface,
	goalResolver routetable.RouteTableInterface,
	opts ...routetable_reconciler.Option,
) Reconciler {
	return NewReconcilerWithLinearRetry(routetable_reconciler.New(routeTableClient, routeTableTrack2Client, goalResolver, opts...), settings)
}

func (f *factory) NewPrivateEndpointConnectionsReconciler(
	settings retry.Settings,
	client privatelinkserviceclient.Interface,
	track2Client azureresources.PrivateLinkServicesInterface,
	goalResolver goalresolvers.PrivateEndpointConnectionsInterface,
	opts ...privatecluster.PrivateEndpointConnectionReconcilerOption,
) Reconciler {
	return NewReconcilerWithLinearRetry(privatecluster.NewPrivateEndpointConnectionReconciler(client, track2Client, goalResolver, opts...), settings)
}

func (f *factory) NewPrivateEndpointReconciler(
	settings retry.Settings,
	resourceGroupTrack2Client azureresources.ResourceGroupInterface,
	privateEndpointClient privateendpointclient.Interface,
	privateEndpointTrack2Client azureresources.PrivateEndpointsInterface,
	goalResolver goalresolvers.PrivateEndpointInterface,
	flags privatecluster.PrivateEndpointReconcilerFlags,
) Reconciler {
	return NewReconcilerWithLinearRetry(privatecluster.NewPrivateEndpointReconciler(resourceGroupTrack2Client, privateEndpointClient, privateEndpointTrack2Client, goalResolver, flags), settings)
}

func (f *factory) NewPrivateDNSRecordReconciler(
	settings retry.Settings,
	resourceGroupTrack2Client azureresources.ResourceGroupInterface,
	client privatednsclient.Interface,
	privateDnsTrack2Client azureresources.PrivateDNSInterface,
	goalResolver goalresolvers.PrivateResourceDNSInterface,
	opts ...privatecluster.PrivateDNSRecordReconcilerOption,
) Reconciler {
	return NewReconcilerWithLinearRetry(privatecluster.NewPrivateDNSRecordReconciler(resourceGroupTrack2Client, client, privateDnsTrack2Client, goalResolver, opts...), settings)
}

func (f *factory) NewPrivateDNSVNetLinkReconciler(
	settings retry.Settings,
	resourceGroupTrack2Client azureresources.ResourceGroupInterface,
	client privatednsclient.Interface,
	privateDNSTrack2Client azureresources.PrivateDNSInterface,
	goalResolver goalresolvers.PrivateResourceDNSInterface,
	opts ...privatecluster.PrivateDNSVNetLinkReconcilerOption,
) Reconciler {
	return NewReconcilerWithLinearRetry(privatecluster.NewPrivateDNSVNetLinkReconciler(resourceGroupTrack2Client, client, privateDNSTrack2Client, goalResolver, opts...), settings)
}

func (f *factory) NewPrivateDNSZoneReconciler(
	settings retry.Settings,
	resourceGroupTrack2Client azureresources.ResourceGroupInterface,
	client privatednsclient.Interface,
	privateDnsTrack2Client azureresources.PrivateDNSInterface,
	goalResolver goalresolvers.PrivateResourceDNSInterface,
	opts ...privatecluster.PrivateDNSZoneReconcilerOption,
) Reconciler {
	return NewReconcilerWithLinearRetry(privatecluster.NewPrivateDNSZoneReconciler(resourceGroupTrack2Client, client, privateDnsTrack2Client, goalResolver, opts...), settings)
}

func (f *factory) NewServiceAssociationLinkReconciler(
	settings retry.Settings,
	vnetClient vnetclient.Interface,
	vnetTrack2Client azureresources.VirtualNetworkInterface,
	subnetClient subnetclient.Interface,
	goalResolver goalresolvers.ServiceAssociationLinkInterface,
	subnetHandlerURL *url.URL,
	opts ...privatecluster.ServiceAssociationLinkReconcilerOption,
) Reconciler {
	return NewReconcilerWithLinearRetry(
		privatecluster.NewServiceAssociationLinkReconciler(
			vnetClient,
			vnetTrack2Client,
			subnetClient,
			subnethandlerclient.New(subnetHandlerURL),
			goalResolver,
			opts...,
		),
		settings,
	)
}

func (f *factory) NewPrivateConnectBalancerReconciler(
	settings retry.Settings,
	lbClient loadbalancer.Interface,
	lbTrack2Client azureresources.LoadbalancersInterface,
	goalResolver goalresolvers.PrivateConnectBalancerInterface,
	opts ...privatecluster.PrivateConnectBalancerReconcilerOption) Reconciler {
	return NewReconcilerWithLinearRetry(privatecluster.NewPrivateConnectBalancerReconciler(lbClient, lbTrack2Client, goalResolver, opts...), settings)
}

func (f *factory) NewVNetReconciler(
	settings retry.Settings,
	client vnetclient.Interface,
	vnetTrack2Client azureresources.VirtualNetworkInterface,
	goalResolver vnet.VNetInterface,
	opts ...vnet_reconciler.Option) Reconciler {
	r := vnet_reconciler.New(client, vnetTrack2Client, goalResolver, opts...)
	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewSubnetReconciler(
	settings retry.Settings,
	client subnetclient.Interface,
	track2Client azureresources.SubnetInterface,
	goalResolver goalresolvers.SubnetInterface,
	opts ...subnet.Option,
) Reconciler {
	return NewReconcilerWithLinearRetry(
		subnet.New(client, track2Client, goalResolver, opts...),
		settings,
	)
}

func (f *factory) NewApplicationSecurityGroupReconciler(
	settings retry.Settings,
	client applicationsecuritygroupclient.Interface,
	track2Client azureresources.ApplicationSecurityGroupInterface,
	goalResolver goalresolvers.ApplicationSecurityGroupInterface,
	opts ...OptionASG,
) Reconciler {
	return NewReconcilerWithLinearRetry(NewApplicationSecurityGroupReconciler(client, track2Client, goalResolver, opts...), settings)
}

func (f *factory) NewNetworkSecurityGroupReconciler(
	settings retry.Settings,
	client securitygroupclient.Interface,
	track2Client azureresources.NetworkSecurityGroupInterface,
	goalResolver goalresolvers.NetworkSecurityGroupInterface,
	opts ...nsg_reconciler.Option,
) Reconciler {
	return NewReconcilerWithLinearRetry(securitygroup.NewNetworkSecurityGroupReconciler(client, track2Client, goalResolver, opts...), settings)
}

func (f *factory) NewBlobStorageReconciler(
	mc *hcpMC.ManagedCluster,
	storageAccountSubscriptionID string,
	storageAccountResourceGroup string,
	storageAccount string,
	containerName string,
	blobClient blobclient.Interface,
	blobContainersClient azureresources.BlobContainersInterface,
	settings retry.Settings,
) Reconciler {
	r := &blobStorageReconciler{
		mc:                           mc,
		StorageAccountSubscriptionID: storageAccountSubscriptionID,
		StorageAccountResourceGroup:  storageAccountResourceGroup,
		StorageAccount:               storageAccount,
		ContainerName:                containerName,
		blobClient:                   blobClient,
		blobContainersClient:         blobContainersClient,
	}
	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewDelegatedCredentialReconciler(
	settings retry.Settings,
	msiCredentialClient msiprotos.Interface,
	msiCredentialStore msiprotos.MSICredentialStore,
	goalResolver goalresolvers.DelegatedCredentialInterface) Reconciler {
	r := &delegatedCredentialReconciler{
		msiCredentialClient:             msiCredentialClient,
		msiCredentialStore:              msiCredentialStore,
		delegatedCredentialGoalResolver: goalResolver,
	}
	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewMSICredentialReconciler(
	settings retry.Settings,
	msiCredentialClient msiprotos.Interface,
	msiCredentialStore msiprotos.MSICredentialStore,
	goalResolver goalresolvers.MSICredentialInterface) Reconciler {
	r := &msiCredentialReconciler{
		msiCredentialClient:       msiCredentialClient,
		msiCredentialStore:        msiCredentialStore,
		msiCredentialGoalResolver: goalResolver,
	}
	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewDeletedMSICredentialReconciler(
	settings retry.Settings,
	msiCredentialStore msiprotos.MSICredentialStore,
	goalResolver goalresolvers.MSICredentialInterface) Reconciler {
	r := &deletedMSICredentialReconciler{
		msiCredentialStore:        msiCredentialStore,
		msiCredentialGoalResolver: goalResolver,
	}
	// a key vault secret can't be purged immediately after the delete operation (conflict error)
	return NewLateStartReconciler(NewReconcilerWithLinearRetry(r, settings), time.After(20*time.Second))
}

func (f *factory) NewDeletedDelegatedCredentialReconciler(
	settings retry.Settings,
	msiCredentialStore msiprotos.MSICredentialStore,
	goalResolver goalresolvers.DelegatedCredentialInterface) Reconciler {
	r := &deletedDelegatedCredentialReconciler{
		msiCredentialStore:              msiCredentialStore,
		delegatedCredentialGoalResolver: goalResolver,
	}
	// a key vault secret can't be purged immediately after the delete operation (conflict error)
	return NewLateStartReconciler(NewReconcilerWithLinearRetry(r, settings), time.After(20*time.Second))
}

func (f *factory) NewUserAssignedIdentityReconciler(
	settings retry.Settings,
	userAssignedIdentityClient userassignedidentityclient.Interface,
	userAssignedIdentityTrack2Client azureresources.UserAssignedIdentitiesInterface,
	goalResolver goalresolvers.UserAssignedIdentityInterface,
	opts ...UserAssignedIdentityReconcilerOption,
) Reconciler {
	r := newUserAssignedIdentityReconciler(userAssignedIdentityClient, userAssignedIdentityTrack2Client, goalResolver, opts...)
	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewManagedClusterOutboundReconciler(
	settings retry.Settings,
	rpHCPClient rphcpclient.RPHCPClientInterface,
	managedClusterOutboundGoalResolver asyncgoalresolvers.ManagedClusterProfileInterface,
	apiTracking *log.APITracking) Reconciler {
	r := &managedClusterOutboundReconciler{
		rpHCPClient:                        rpHCPClient,
		apiTracking:                        apiTracking,
		managedClusterOutboundGoalResolver: managedClusterOutboundGoalResolver,
	}

	return NewReconcilerWithLinearRetry(r, settings)
}

func (f *factory) NewContainerRegistryReconciler(
	settings retry.Settings,
	containerRegistryClient azureresources.ContainerRegistryInterface,
	containerRegistryCacheRuleClient azureresources.ContainerRegistryCacheRuleInterface,
	goalResolver goalresolvers.ContainerRegistryInterface,
) Reconciler {
	return NewReconcilerWithLinearRetry(&containerRegistryReconciler{
		containerRegistryClient:          containerRegistryClient,
		containerRegistryCacheRuleClient: containerRegistryCacheRuleClient,
		containerRegistryGoalResolver:    goalResolver,
	}, settings)
}

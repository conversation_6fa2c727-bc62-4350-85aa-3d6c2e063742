package reconcilers

//go:generate sh -c "GO111MODULE=on mockgen --build_flags=--mod=mod go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers FactoryInterface,Reconciler>./mock_$GOPACKAGE/interfaces.go"

import (
	"net/url"

	msiprotos "go.goms.io/aks/rp/msi/protos"
	hcpMC "go.goms.io/aks/rp/protos/hcp/types/managedcluster/type/v1"
	asyncgoalresolvers "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/goalresolvers"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/goalresolvers/routetable"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/goalresolvers/vnet"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/rawgoalretriever"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/goalresolvers"
	outbound_reconciler "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/reconciler"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/privatecluster"
	routetable_reconciler "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/routetable"
	nsg_reconciler "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/securitygroup"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/subnet"
	vnet_reconciler "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/vnet"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/rphcpclient"
	"go.goms.io/aks/rp/toolkit/azureclients/applicationsecuritygroupclient"
	"go.goms.io/aks/rp/toolkit/azureclients/blobclient"
	"go.goms.io/aks/rp/toolkit/azureclients/denyassignmentclient"
	"go.goms.io/aks/rp/toolkit/azureclients/dnsclient"
	"go.goms.io/aks/rp/toolkit/azureclients/loadbalancer"
	"go.goms.io/aks/rp/toolkit/azureclients/natgatewayclient"
	"go.goms.io/aks/rp/toolkit/azureclients/networkinterfaceclient"
	"go.goms.io/aks/rp/toolkit/azureclients/privatednsclient"
	"go.goms.io/aks/rp/toolkit/azureclients/privateendpointclient"
	"go.goms.io/aks/rp/toolkit/azureclients/privatelinkserviceclient"
	"go.goms.io/aks/rp/toolkit/azureclients/publicipaddress"
	"go.goms.io/aks/rp/toolkit/azureclients/publicipprefix"
	"go.goms.io/aks/rp/toolkit/azureclients/resourceprovider"
	"go.goms.io/aks/rp/toolkit/azureclients/routetableclient"
	"go.goms.io/aks/rp/toolkit/azureclients/securitygroupclient"
	"go.goms.io/aks/rp/toolkit/azureclients/subnetclient"
	"go.goms.io/aks/rp/toolkit/azureclients/userassignedidentityclient"
	"go.goms.io/aks/rp/toolkit/azureclients/vmclient"
	"go.goms.io/aks/rp/toolkit/azureclients/vmssclient"
	"go.goms.io/aks/rp/toolkit/azureclients/vnetclient"
	"go.goms.io/aks/rp/toolkit/log"
	"go.goms.io/aks/rp/toolkit/retry"
)

type FactoryInterface interface {
	NewResourceProviderRegistrationReconciler(
		retry.Settings,
		resourceprovider.Interface,
		azureresources.ResourceProvidersInterface,
		goalresolvers.ResourceProviderRegistrationInterface,
		...ResourceProviderRegistrationReconcilerOption,
	) Reconciler
	NewDNSReconciler(
		settings retry.Settings,
		dnsClient dnsclient.Interface,
		dnsRecordSetTrack2Client azureresources.DNSRecordSetInterface,
		dnsGoalResolver goalresolvers.DNSInterface,
		mcResourceId string,
		ccpId string,
		opts ...DNSReconcilerOption,
	) Reconciler
	NewResourceGroupReconciler(retry.Settings, azureresources.ResourceGroupInterface, goalresolvers.ResourceGroupInterface, bool, ResourceGroupReconcilerFlags) Reconciler
	NewDenyAssignmentReconciler(settings retry.Settings, daClient denyassignmentclient.Interface, daGoalResolver goalresolvers.DenyAssignmentInterface) Reconciler
	NewOutboundReconciler(
		retry.Settings,
		resourceprovider.Interface,
		azureresources.ResourceProvidersInterface,
		publicipaddress.Interface,
		azureresources.PublicIPAddressInterface,
		publicipprefix.Interface,
		azureresources.PublicIPPrefixesInterface,
		loadbalancer.Interface,
		subnetclient.Interface,
		azureresources.SubnetInterface,
		natgatewayclient.Interface,
		azureresources.NatGatewaysInterface,
		vmssclient.Interface,
		vmclient.Interface,
		azureresources.VirtualMachineInterface,
		networkinterfaceclient.Interface,
		azureresources.NetworkInterfacesInterface,
		goalresolvers.OutboundInterface,
		rawgoalretriever.AgentPoolsInterface,
		bool,
		outbound_reconciler.OutboundReconcilerFlags,
	) Reconciler
	NewRouteTableReconciler(
		retry.Settings,
		routetableclient.Interface,
		azureresources.RouteTablesInterface,
		routetable.RouteTableInterface,
		...routetable_reconciler.Option,
	) Reconciler
	NewPrivateEndpointConnectionsReconciler(retry.Settings, privatelinkserviceclient.Interface, azureresources.PrivateLinkServicesInterface, goalresolvers.PrivateEndpointConnectionsInterface, ...privatecluster.PrivateEndpointConnectionReconcilerOption) Reconciler
	NewPrivateEndpointReconciler(retry.Settings, azureresources.ResourceGroupInterface, privateendpointclient.Interface, azureresources.PrivateEndpointsInterface, goalresolvers.PrivateEndpointInterface, privatecluster.PrivateEndpointReconcilerFlags) Reconciler
	NewPrivateDNSZoneReconciler(retry.Settings, azureresources.ResourceGroupInterface, privatednsclient.Interface, azureresources.PrivateDNSInterface, goalresolvers.PrivateResourceDNSInterface, ...privatecluster.PrivateDNSZoneReconcilerOption) Reconciler
	NewPrivateDNSRecordReconciler(retry.Settings, azureresources.ResourceGroupInterface, privatednsclient.Interface, azureresources.PrivateDNSInterface, goalresolvers.PrivateResourceDNSInterface, ...privatecluster.PrivateDNSRecordReconcilerOption) Reconciler
	NewPrivateDNSVNetLinkReconciler(retry.Settings, azureresources.ResourceGroupInterface, privatednsclient.Interface, azureresources.PrivateDNSInterface, goalresolvers.PrivateResourceDNSInterface, ...privatecluster.PrivateDNSVNetLinkReconcilerOption) Reconciler
	NewServiceAssociationLinkReconciler(
		settings retry.Settings,
		vnetClient vnetclient.Interface,
		vnetTrack2Client azureresources.VirtualNetworkInterface,
		subnetClient subnetclient.Interface,
		goalResolver goalresolvers.ServiceAssociationLinkInterface,
		subnetHandlerURL *url.URL,
		opts ...privatecluster.ServiceAssociationLinkReconcilerOption,
	) Reconciler
	NewPrivateConnectBalancerReconciler(retry.Settings, loadbalancer.Interface, azureresources.LoadbalancersInterface, goalresolvers.PrivateConnectBalancerInterface, ...privatecluster.PrivateConnectBalancerReconcilerOption) Reconciler
	NewVNetReconciler(retry.Settings, vnetclient.Interface, azureresources.VirtualNetworkInterface, vnet.VNetInterface, ...vnet_reconciler.Option) Reconciler
	NewSubnetReconciler(
		retry.Settings,
		subnetclient.Interface,
		azureresources.SubnetInterface,
		goalresolvers.SubnetInterface,
		...subnet.Option,
	) Reconciler
	NewApplicationSecurityGroupReconciler(
		retry.Settings,
		applicationsecuritygroupclient.Interface,
		azureresources.ApplicationSecurityGroupInterface,
		goalresolvers.ApplicationSecurityGroupInterface,
		...OptionASG,
	) Reconciler
	NewNetworkSecurityGroupReconciler(
		retry.Settings,
		securitygroupclient.Interface,
		azureresources.NetworkSecurityGroupInterface,
		goalresolvers.NetworkSecurityGroupInterface,
		...nsg_reconciler.Option,
	) Reconciler

	NewBlobStorageReconciler(
		mc *hcpMC.ManagedCluster,
		storageAccountSubscriptionID string,
		storageAccountResourceGroup string,
		storageAccount string,
		containerName string,
		blobClient blobclient.Interface,
		blobContainersClient azureresources.BlobContainersInterface,
		settings retry.Settings,
	) Reconciler

	NewMSICredentialReconciler(retry.Settings, msiprotos.Interface, msiprotos.MSICredentialStore, goalresolvers.MSICredentialInterface) Reconciler
	NewDelegatedCredentialReconciler(retry.Settings, msiprotos.Interface, msiprotos.MSICredentialStore, goalresolvers.DelegatedCredentialInterface) Reconciler
	NewDeletedMSICredentialReconciler(retry.Settings, msiprotos.MSICredentialStore, goalresolvers.MSICredentialInterface) Reconciler
	NewDeletedDelegatedCredentialReconciler(retry.Settings, msiprotos.MSICredentialStore, goalresolvers.DelegatedCredentialInterface) Reconciler
	NewUserAssignedIdentityReconciler(retry.Settings, userassignedidentityclient.Interface, azureresources.UserAssignedIdentitiesInterface, goalresolvers.UserAssignedIdentityInterface, ...UserAssignedIdentityReconcilerOption) Reconciler
	NewManagedClusterOutboundReconciler(retry.Settings, rphcpclient.RPHCPClientInterface, asyncgoalresolvers.ManagedClusterProfileInterface, *log.APITracking) Reconciler
	NewContainerRegistryReconciler(retry.Settings, azureresources.ContainerRegistryInterface, azureresources.ContainerRegistryCacheRuleInterface, goalresolvers.ContainerRegistryInterface) Reconciler
}

package vm

import (
	"context"
	"fmt"
	"strings"
	"sync"

	azto "github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute/v5"
	"github.com/Azure/go-autorest/autorest/to"

	hcpEnums "go.goms.io/aks/rp/protos/hcp/types/enums/v1"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool/vm/nic"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/common/vmhelper"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/tags"
	"go.goms.io/aks/rp/toolkit/apierror"
	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
	"go.goms.io/aks/rp/toolkit/log"
)

type VMAgentpoolBackendpoolReconcilerV2 struct {
	flags         ReconcilerFlags
	vmClient      azureresources.VirtualMachineInterface
	nicReconciler nic.NicInterfaceReconciler
}

func NewV2(flags ReconcilerFlags, vmClient azureresources.VirtualMachineInterface, interfaceClient azureresources.NetworkInterfacesInterface) agentpool.AgentPoolLBBackendpoolReconciler {
	return &VMAgentpoolBackendpoolReconcilerV2{
		flags:         flags,
		vmClient:      vmClient,
		nicReconciler: nic.NewV2(interfaceClient),
	}
}

func (c *VMAgentpoolBackendpoolReconcilerV2) DecoupleLBBackendPool(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}) *cgerror.CategorizedError {
	ctx, span := log.StartSpan(ctx, "DecoupleLBBackendPool", log.AKSTeamNetworkIntegration)
	defer span.End()
	nicMap, cerr := c.listAllOfVMInterfaces(ctx, computeSubscriptionID, resourceGroupName)
	if cerr != nil {
		return cerr
	}
	return c.nicReconciler.DecoupleNicInterfaceWithLBBackendpool(ctx, networkSubscriptionID, resourceGroupName, nicMap, backendpoolIDs, backendpoolIDsIPV6)
}

func (c *VMAgentpoolBackendpoolReconcilerV2) AssociateLBBackendpool(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
	// Get all VMs and their NICs, categorized by exclusion status
	normalNicMap, excludedNicMap, cerr := c.listAllOfVMInterfacesWithExclusionCategories(ctx, computeSubscriptionID, resourceGroupName, excludedAgentPoolNames)
	if cerr != nil {
		return cerr
	}

	// Normal VMs (not excluded) can join all backend pools
	if len(normalNicMap) > 0 {
		if err := c.nicReconciler.AssociateNicInterfaceWithLBBackendpool(ctx, networkSubscriptionID, resourceGroupName, normalNicMap, backendpoolIDs, backendpoolIDsIPV6); err != nil {
			return err
		}
	}

	// Excluded VMs can only join outbound backend pools
	if len(excludedNicMap) > 0 {
		filteredBackendpoolIDs := agentpool.FilterBackendPoolsForExclusion(ctx, backendpoolIDs, true)
		filteredBackendpoolIDsIPV6 := agentpool.FilterBackendPoolsForExclusion(ctx, backendpoolIDsIPV6, true)
		if err := c.nicReconciler.AssociateNicInterfaceWithLBBackendpool(ctx, networkSubscriptionID, resourceGroupName, excludedNicMap, filteredBackendpoolIDs, filteredBackendpoolIDsIPV6); err != nil {
			return err
		}
	}

	return nil
}

func (c *VMAgentpoolBackendpoolReconcilerV2) listAllOfVMInterfaces(ctx context.Context, computeSubscriptionID string, resourceGroupName string) (map[string]struct{}, *cgerror.CategorizedError) {
	normalNicMap, excludedNicMap, cerr := c.listAllOfVMInterfacesWithExclusionCategories(ctx, computeSubscriptionID, resourceGroupName, nil)
	if cerr != nil {
		return nil, cerr
	}

	// Merge the two nic lists to get all nics
	allNicMap := make(map[string]struct{})
	for nicID := range normalNicMap {
		allNicMap[nicID] = struct{}{}
	}
	for nicID := range excludedNicMap {
		allNicMap[nicID] = struct{}{}
	}

	return allNicMap, nil
}

func (c *VMAgentpoolBackendpoolReconcilerV2) listAllOfVMInterfacesWithExclusionCategories(ctx context.Context, computeSubscriptionID string, resourceGroupName string, excludedAgentPoolNames map[string]struct{}) (map[string]struct{}, map[string]struct{}, *cgerror.CategorizedError) {
	logger := log.MustGetLogger(ctx)
	var lstOpts *azureresources.ListVirtualMachinesOptions
	// If ARG is not enabled, we won't use ExpandTypeForListVMsInstanceView as it only works for VMSS
	// This also means the returned VMs won't have InstanceView, so we will go fetch them separately
	if c.flags.EnableListNicsUsingARG(ctx, logger) {
		lstOpts = &azureresources.ListVirtualMachinesOptions{
			VirtualMachinesClientListOptions: &armcompute.VirtualMachinesClientListOptions{
				Expand: azto.Ptr(armcompute.ExpandTypeForListVMsInstanceView),
			},
			UseResourceGraphCache: true,
		}
	}

	vmList, err := c.vmClient.ListAll(ctx, resourceGroupName, lstOpts)
	if err != nil {
		return nil, nil, err
	}
	var normalNicMap, excludedNicMap map[string]struct{}
	if len(vmList) == 0 {
		return normalNicMap, excludedNicMap, nil
	}

	mu := sync.Mutex{}
	populateNicMapFunc := func(ctx context.Context, vm *armcompute.VirtualMachine) *cgerror.CategorizedError {
		poolNameTag := tags.GetTagValueWithFallbackToOldKey(vm.Tags, tags.PoolName, tags.OldPoolName)
		if poolNameTag == nil {
			logger.Warning(ctx, "vm is not associated with pool")
			return nil
		}

		// 1. Use poolNameTag to determine if the VM is in excluded list
		var isExcludedAgentPool bool
		if excludedAgentPoolNames != nil {
			if _, excluded := excludedAgentPoolNames[*poolNameTag]; excluded {
				isExcludedAgentPool = true
				logger.Infof(ctx, "VM %s belongs to excluded agent pool %s - will only join outbound pools", to.String(vm.Name), *poolNameTag)
			}
		}

		if vm == nil || vm.Properties == nil || vm.Properties.NetworkProfile == nil || len(vm.Properties.NetworkProfile.NetworkInterfaces) == 0 {
			logger.Warning(ctx, "vm is not associated with nic")
			return nil
		}
		if azureresources.StringPtrEqual(vm.Properties.ProvisioningState, "Deallocating") {
			err := fmt.Errorf("listAllOfVMInterfaces: found virtual machine %s being deleted, will retry later", to.String(vm.Name))
			// Not sure why we need to log a categorized error here but that's what the Track1 logic does
			cerr := cgerror.NewCategorizedErrorWithCodeAndMessage(
				ctx,
				apierror.ClientError,
				hcpEnums.ErrorCode_FailedToListNetworkInterfaces,
				cgerror.FailedVMIsBeingDeleted,
				err.Error(),
				cgerror.VirtualMachines,
				err,
			)
			logger.Warning(ctx, cerr.Error())
			return nil
		}
		// ARG team support intanceView expand with sp 1st party token, but not support msi
		// to be more robust, if the instanceview is nil, need to call getInstanceView for each vm
		if vm.Properties.InstanceView == nil {
			resp, cerr := c.vmClient.InstanceView(ctx, resourceGroupName, *vm.Name, nil)
			if cerr != nil {
				return cerr
			}
			vm.Properties.InstanceView = &resp.VirtualMachineInstanceView
		}
		if vm.Properties.InstanceView != nil && vmhelper.DeallocatedAndStoppedVMFilterTrack2(ctx, vm.Properties.InstanceView) {
			logger.Warning(ctx, "vm is deallocated or stopped")
			return nil
		}

		nicID := getVMPrimaryNetworkInterfaceConfigurationV2(vm.Properties.NetworkProfile.NetworkInterfaces)
		if nicID == nil {
			logger.Warning(ctx, "failed to find primary nic")
			return nil
		}

		mu.Lock()
		defer mu.Unlock()

		// 2. If not excluded, add the VM to normal list
		// 3. If excluded, add the VM to excluded list
		if isExcludedAgentPool {
			if excludedNicMap == nil {
				excludedNicMap = make(map[string]struct{})
			}
			excludedNicMap[strings.ToLower(*nicID)] = struct{}{}
		} else {
			if normalNicMap == nil {
				normalNicMap = make(map[string]struct{})
			}
			normalNicMap[strings.ToLower(*nicID)] = struct{}{}
		}
		return nil
	}

	var cerr *cgerror.CategorizedError
	setCerrOnce := sync.Once{}
	wg := sync.WaitGroup{}
	cancelableCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	for _, vm := range vmList {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if ctxErr := cancelableCtx.Err(); ctxErr != nil {
				// If goroutine not started but an error has been returned by another goroutine, don't do anything
				return
			}
			populateErr := populateNicMapFunc(cancelableCtx, vm)
			if populateErr != nil {
				setCerrOnce.Do(func() {
					// cancel on the first error, that's what we're going to return
					// it's useless to continue or log any error (as most of them will be context canceled) if we have got an error so quit fast
					cerr = populateErr
					cancel()
				})
			}
		}()
	}
	wg.Wait()

	if cerr != nil {
		return nil, nil, cerr
	}
	return normalNicMap, excludedNicMap, nil
}

func getVMPrimaryNetworkInterfaceConfigurationV2(networkConfigurations []*armcompute.NetworkInterfaceReference) *string {
	if len(networkConfigurations) == 1 {
		return networkConfigurations[0].ID
	}

	for idx := range networkConfigurations {
		networkConfig := networkConfigurations[idx]
		if networkConfig.Properties != nil && networkConfig.Properties.Primary != nil && *networkConfig.Properties.Primary {
			return networkConfig.ID
		}
	}

	return nil
}

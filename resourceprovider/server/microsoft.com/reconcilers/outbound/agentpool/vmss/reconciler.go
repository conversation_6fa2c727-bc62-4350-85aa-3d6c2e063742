package vmss

import (
	"context"
	"fmt"
	"strings"

	"github.com/Azure/azure-sdk-for-go/services/compute/mgmt/2022-03-01/compute"
	"github.com/Azure/go-autorest/autorest/to"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/common/vmsshelper"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/tags"
	"go.goms.io/aks/rp/toolkit/apierror"
	"go.goms.io/aks/rp/toolkit/azureclients/vmssclient"
	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
	"go.goms.io/aks/rp/toolkit/log"
)

type VMSSSLBBackendpoolReconciler struct {
	vmssClient vmssclient.Interface
}

func New(vmssClient vmssclient.Interface) agentpool.AgentPoolLBBackendpoolReconciler {
	return &VMSSSLBBackendpoolReconciler{
		vmssClient: vmssClient,
	}
}

func (c *VMSSSLBBackendpoolReconciler) DecoupleLBBackendPool(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIPv6IDs map[string]struct{}) *cgerror.CategorizedError {
	ctx, span := log.StartSpan(ctx, "DecoupleLBBackendPool", log.AKSTeamNetworkIntegration)
	defer span.End()
	vmssList, _, err := c.vmssClient.ListVirtualMachineScaleSets(ctx, computeSubscriptionID, resourceGroupName, false)
	if err != nil {
		return err
	}
	for _, vmss := range vmssList {
		vmss := vmss
		rerr := c.DecoupleLBBackendPoolFromVMSS(ctx, computeSubscriptionID, resourceGroupName, &vmss, backendpoolIDs, backendpoolIPv6IDs)
		if rerr != nil {
			return rerr
		}
	}
	return nil
}

// TODO: shafan add support for single vm
// DecoupleLBBackendPoolFromVMSS decouple the agent pool SLB from the VMSS and VMSS instances
func (c *VMSSSLBBackendpoolReconciler) DecoupleLBBackendPoolFromVMSS(ctx context.Context, managedClusterSubscriptionID string, resourceGroupName string, vmss *compute.VirtualMachineScaleSet, backendpoolIDs, backendpoolIPv6IDs map[string]struct{}) *cgerror.CategorizedError {
	if poolNameTag := tags.GetTagValueWithFallbackToOldKey(vmss.Tags, tags.PoolName, tags.OldPoolName); poolNameTag == nil {
		log.GetLogger(ctx).Infof(ctx, "associateVMSSWithAgentPoolSLB: vmss %s is not an agent pool vmss", to.String(vmss.Name))
		return nil
	}
	if vmss.VirtualMachineScaleSetProperties == nil || vmss.VirtualMachineProfile == nil || vmss.VirtualMachineProfile.NetworkProfile == nil ||
		vmss.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations == nil {
		err := fmt.Errorf("DecoupleLBBackendPoolFromVMSS: failed to read the network interface configuration of vmss %s", to.String(vmss.Name))
		cerr := cgerror.NewCategorizedError(ctx, apierror.InternalError, cgerror.FailedValidateVMSSParameters, cgerror.VMSS, err)
		log.GetLogger(ctx).Error(ctx, cerr.Error())
		return cerr
	}

	if vmss.ProvisioningState != nil && strings.EqualFold(to.String(vmss.ProvisioningState), "Deallocating") {
		err := fmt.Errorf("DecoupleLBBackendPoolFromVMSS: found VMSS %s being deleted, will retry later", to.String(vmss.Name))
		cerr := cgerror.NewCategorizedError(ctx, apierror.ClientError, cgerror.FailedVMSSIsBeingDeleted, cgerror.VMSS, err)
		log.GetLogger(ctx).Warning(ctx, cerr.Error())
		return nil
	}

	var changed bool = false
	vmssNics := *vmss.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations
	for nicIndex := 0; nicIndex < len(vmssNics); nicIndex++ {
		if vmssNics[nicIndex].IPConfigurations == nil {
			log.GetLogger(ctx).Warningf(ctx, "vmssNetworkConfig.IPConfigurations is nil")
			continue
		}
		for ipCfgIndex := 0; ipCfgIndex < len(*vmssNics[nicIndex].IPConfigurations); ipCfgIndex++ {
			ipCfgChanged, rerr := decoupleLoadBalancerBackendPools(ctx, managedClusterSubscriptionID, resourceGroupName, to.String(vmss.Name), &(*vmssNics[nicIndex].IPConfigurations)[ipCfgIndex], backendpoolIDs, backendpoolIPv6IDs)
			if rerr != nil {
				return rerr
			}
			changed = changed || ipCfgChanged
		}
	}

	if !changed {
		log.GetLogger(ctx).Infof(ctx, "DecoupleLBBackendPoolFromVMSS: no need to update VMSS %s", to.String(vmss.Name))
	} else {

		newVMSS := &compute.VirtualMachineScaleSet{
			Location: vmss.Location,
			VirtualMachineScaleSetProperties: &compute.VirtualMachineScaleSetProperties{
				VirtualMachineProfile: &compute.VirtualMachineScaleSetVMProfile{
					NetworkProfile: &compute.VirtualMachineScaleSetNetworkProfile{
						NetworkInterfaceConfigurations: &vmssNics,
					},
				},
			},
		}

		log.GetLogger(ctx).Infof(ctx, "DecoupleLBBackendPoolFromVMSS: updating VMSS %s", to.String(vmss.Name))
		_, cerr := c.vmssClient.CreateOrUpdateVirtualMachineScaleSet(ctx, managedClusterSubscriptionID, resourceGroupName, to.String(vmss.Name), newVMSS)
		if cerr != nil {
			cerr = cgerror.ToGenericInternalErrorIfNotSet(cerr).SetDependencyIfNotSet(cgerror.VMSS)
			log.GetLogger(ctx).Errorf(ctx, "DecoupleLBBackendPoolFromVMSS: failed to CreateOrUpdateVMSS: %s", cerr)
			return cerr
		}
	}

	log.GetLogger(ctx).Infof(ctx, "DecoupleLBBackendPoolFromVMSS: upgrading all instances of VMSS %s", to.String(vmss.Name))
	//long time operatiion
	vmList, cerr := c.vmssClient.ListVirtualMachineScaleSetVMsWithInstanceViews(ctx, managedClusterSubscriptionID, resourceGroupName, to.String(vmss.Name))
	if cerr != nil {
		log.GetLogger(ctx).Errorf(ctx, "DecoupleLBBackendPoolFromVMSS: failed to listVMSSvm: %s", cerr)
		return cerr
	}
	for _, vm := range vmList {
		changed = false
		if vm.VirtualMachineScaleSetVMProperties == nil || vm.VirtualMachineScaleSetVMProperties.NetworkProfileConfiguration == nil ||
			vm.VirtualMachineScaleSetVMProperties.NetworkProfileConfiguration.NetworkInterfaceConfigurations == nil {
			err := fmt.Errorf("DecoupleLBBackendPoolFromVMSS: failed to read the network interface configuration of vmss %s vm %s", to.String(vmss.Name), *vm.Name)
			cerr := cgerror.NewCategorizedError(ctx, apierror.InternalError, cgerror.FailedConstructVMSSObject, cgerror.VMSS, err)
			log.GetLogger(ctx).Error(ctx, cerr.Error())
			return cerr
		}
		if vm.ProvisioningState != nil && strings.EqualFold(to.String(vm.ProvisioningState), "Deallocating") {
			log.GetLogger(ctx).Infof(ctx, "DecoupleLBBackendPoolFromVMSS: found VMSS %s vm %s being deleted or not valid, will ignore it", to.String(vmss.Name), to.String(vm.Name))
			continue
		}
		if vm.InstanceView == nil {
			newvm, err := c.vmssClient.GetVirtualMachineScaleSetVMWithInstanceViews(ctx, managedClusterSubscriptionID, resourceGroupName, to.String(vmss.Name), to.String(vm.InstanceID))
			if err != nil {
				log.GetLogger(ctx).Errorf(ctx, "associateVMSSWithAgentPoolSLB: failed to get the instance view of vmss %s, vm %s", to.String(vmss.Name), to.String(vm.Name))
				return err
			}
			vm = *newvm
		}
		if vm.InstanceView != nil && vm.InstanceView.Statuses != nil {
			if vmsshelper.DeallocatedAndStoppedVMFilter(ctx, vm) {
				log.GetLogger(ctx).Infof(ctx, "DecoupleLBBackendPoolFromVMSS: found VMSS %s vm %s is stopped or deallocated, will ignore it", to.String(vmss.Name), to.String(vm.Name))
				continue
			}
		}
		vmNics := *vm.NetworkProfileConfiguration.NetworkInterfaceConfigurations
		for nicIndex := 0; nicIndex < len(vmNics); nicIndex++ {
			if vmNics[nicIndex].IPConfigurations == nil {
				err := fmt.Errorf("vmssNetworkConfig.IPConfigurations is nil")
				cerr := cgerror.ToGenericInternalErrorIfNotSet(err).SetDependencyIfNotSet(cgerror.VMSS)
				log.GetLogger(ctx).Warning(ctx, cerr.Error())
				continue
			}
			for ipCfgIndex := 0; ipCfgIndex < len(*vmNics[nicIndex].IPConfigurations); ipCfgIndex++ {
				ipCfgChanged, rerr := decoupleLoadBalancerBackendPools(ctx, managedClusterSubscriptionID, resourceGroupName, to.String(vmss.Name), &(*vmNics[nicIndex].IPConfigurations)[ipCfgIndex], backendpoolIDs, backendpoolIPv6IDs)
				if rerr != nil {
					return rerr
				}
				changed = changed || ipCfgChanged
			}
		}

		if !changed {
			continue
		}
		if vm.ProvisioningState != nil {
			log.GetLogger(ctx).Infof(ctx, "DecoupleLBBackendPoolFromVMSS: update VMSS %s vm %s in %s state", to.String(vmss.Name), to.String(vm.Name), to.String(vm.ProvisioningState))
		}
		newVMParams := compute.VirtualMachineScaleSetVM{
			VirtualMachineScaleSetVMProperties: &compute.VirtualMachineScaleSetVMProperties{
				NetworkProfileConfiguration: &compute.VirtualMachineScaleSetVMNetworkProfileConfiguration{
					NetworkInterfaceConfigurations: &vmNics,
				},
			},
		}
		_, cerr := c.vmssClient.UpdateVirtualMachineScaleSetVM(ctx, managedClusterSubscriptionID, resourceGroupName, to.String(vmss.Name), to.String(vm.InstanceID), &newVMParams)
		if cerr != nil {
			if cerr.SubCode == cgerror.InvalidParameter && strings.Contains(cerr.OriginError.Error(), "is not an active Virtual Machine Scale Set VM") {
				log.GetLogger(ctx).Infof(ctx, "DecoupleLBBackendPoolFromVMSS: found VMSS %s vm %s is not active, will ignore it", to.String(vmss.Name), to.String(vm.Name))
				continue
			}
			log.GetLogger(ctx).Errorf(ctx, "DecoupleLBBackendPoolFromVMSS: failed to update VMSS instances: %s", cerr)
			return cerr
		}
	}
	return nil
}

func (c *VMSSSLBBackendpoolReconciler) AssociateLBBackendpool(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
	vmssList, _, err := c.vmssClient.ListVirtualMachineScaleSets(ctx, computeSubscriptionID, resourceGroupName, false)
	if err != nil {
		return err
	}
	for _, vmss := range vmssList {
		vmss := vmss

		// Check if this VMSS belongs to an excluded agent pool
		var isExcludedAgentPool bool
		if poolNameTag := tags.GetTagValueWithFallbackToOldKey(vmss.Tags, tags.PoolName, tags.OldPoolName); poolNameTag != nil {
			if _, excluded := excludedAgentPoolNames[*poolNameTag]; excluded {
				isExcludedAgentPool = true
				log.GetLogger(ctx).Infof(ctx, "VMSS %s belongs to excluded agent pool %s - will only join outbound pools", to.String(vmss.Name), *poolNameTag)
			}
		}

		// Filter backend pools based on exclusion - excluded agent pools only join outbound pools
		filteredBackendpoolIDs := agentpool.FilterBackendPoolsForExclusion(ctx, backendpoolIDs, isExcludedAgentPool)
		filteredBackendpoolIDsIPV6 := agentpool.FilterBackendPoolsForExclusion(ctx, backendpoolIDsIPV6, isExcludedAgentPool)

		if cerr := c.AssociateLBBackendpoolWithVMSS(ctx, computeSubscriptionID, resourceGroupName, &vmss, filteredBackendpoolIDs, filteredBackendpoolIDsIPV6); cerr != nil {
			return cerr
		}
	}
	return nil
}

// assiociateVMSSFromAgentPoolSLB associate the agent pool SLB with the VMSS and VMSS instances
func (c *VMSSSLBBackendpoolReconciler) AssociateLBBackendpoolWithVMSS(ctx context.Context, managedClusterSubscriptionID string, resourceGroupName string, vmss *compute.VirtualMachineScaleSet, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}) *cgerror.CategorizedError {
	if poolNameTag := tags.GetTagValueWithFallbackToOldKey(vmss.Tags, tags.PoolName, tags.OldPoolName); poolNameTag == nil {
		log.GetLogger(ctx).Infof(ctx, "associateVMSSWithAgentPoolSLB: vmss %s is not an agent pool vmss", to.String(vmss.Name))
		return nil
	}
	if vmss.VirtualMachineScaleSetProperties == nil || vmss.VirtualMachineProfile == nil || vmss.VirtualMachineProfile.NetworkProfile == nil ||
		vmss.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations == nil {
		err := fmt.Errorf("associateVMSSWithAgentPoolSLB: failed to read the network interface configuration of vmss %s", to.String(vmss.Name))
		cerr := cgerror.NewCategorizedError(ctx, apierror.InternalError, cgerror.FailedValidateVMSSParameters, cgerror.VMSS, err)
		log.GetLogger(ctx).Error(ctx, cerr.Error())
		return cerr
	}
	if vmss.ProvisioningState != nil && strings.EqualFold(to.String(vmss.ProvisioningState), "Deallocating") {
		err := fmt.Errorf("associateVMSSWithAgentPoolSLB: found VMSS %s being deleted, will retry later", to.String(vmss.Name))
		cerr := cgerror.NewCategorizedError(ctx, apierror.ClientError, cgerror.FailedToDeleteVMSS, cgerror.VMSS, err)
		log.GetLogger(ctx).Warning(ctx, cerr.Error())
		return nil
	}

	vmssNics := *vmss.VirtualMachineProfile.NetworkProfile.NetworkInterfaceConfigurations
	primaryNIC, err := getPrimaryNetworkInterfaceConfiguration(vmssNics)
	if err != nil {
		log.GetLogger(ctx).Errorf(ctx, "associateVMSSWithAgentPoolSLB: failed to get the primary network interface of the VMSS %s: %s", to.String(vmss.Name), err.Error())
		cerr := cgerror.NewCategorizedError(ctx, apierror.ClientError, cgerror.FailedValidateVMSSParameters, cgerror.VMSS, err)
		return cerr
	}

	primaryIPConfig, err := getPrimaryIPConfigFromVMSSNetworkConfig(primaryNIC)
	if err != nil {
		log.GetLogger(ctx).Errorf(ctx, "associateVMSSWithAgentPoolSLB: failed to get the primary IP config from the VMSS %s", to.String(vmss.Name))
		cerr := cgerror.NewCategorizedError(ctx, apierror.ClientError, cgerror.FailedValidateVMSSParameters, cgerror.VMSS, err)
		return cerr
	}

	foundIPv4 := associateLoadBalancerOutBoundPools(ctx, to.String(vmss.Name), primaryIPConfig, backendpoolIDs)
	var foundIPv6 bool
	if len(backendpoolIDsIPV6) > 0 {
		ipv6IPConfig := getIPv6ConfigFromVMSSNetworkConfig(primaryNIC)
		foundIPv6 = associateLoadBalancerOutBoundPools(ctx, to.String(vmss.Name), ipv6IPConfig, backendpoolIDsIPV6)
	}
	if !foundIPv4 && !foundIPv6 {
		log.GetLogger(ctx).Infof(ctx, "associateVMSSWithAgentPoolSLB: no need to update VMSS %s", to.String(vmss.Name))
	} else {
		newVMSS := &compute.VirtualMachineScaleSet{
			Location: vmss.Location,
			VirtualMachineScaleSetProperties: &compute.VirtualMachineScaleSetProperties{
				VirtualMachineProfile: &compute.VirtualMachineScaleSetVMProfile{
					NetworkProfile: &compute.VirtualMachineScaleSetNetworkProfile{
						NetworkInterfaceConfigurations: &vmssNics,
					},
				},
			},
		}
		log.GetLogger(ctx).Infof(ctx, "associateVMSSWithAgentPoolSLB: updating VMSS %s", to.String(vmss.Name))
		_, cerr := c.vmssClient.CreateOrUpdateVirtualMachineScaleSet(ctx, managedClusterSubscriptionID, resourceGroupName, to.String(vmss.Name), newVMSS)
		if cerr != nil {
			log.GetLogger(ctx).Errorf(ctx, "associateVMSSWithAgentPoolSLB: failed to CreateOrUpdateVMSS: %s", cerr)
			return cerr
		}
	}

	log.GetLogger(ctx).Infof(ctx, "associateVMSSWithAgentPoolSLB: upgrading all instances of VMSS %s", to.String(vmss.Name))
	//long time operatiion
	vmList, cerr := c.vmssClient.ListVirtualMachineScaleSetVMsWithInstanceViews(ctx, managedClusterSubscriptionID, resourceGroupName, to.String(vmss.Name))
	if cerr != nil {
		log.GetLogger(ctx).Errorf(ctx, "associateVMSSWithAgentPoolSLB: failed to listVMSSvm: %s", cerr)
		return cerr
	}
	for _, vm := range vmList {
		vm := vm
		if vm.VirtualMachineScaleSetVMProperties == nil || vm.NetworkProfileConfiguration == nil || vm.NetworkProfileConfiguration.NetworkInterfaceConfigurations == nil {
			err := fmt.Errorf("associateVMSSWithAgentPoolSLB: failed to read the network interface configuration of vmss %s, vm %s", to.String(vmss.Name), to.String(vm.Name))
			cerr := cgerror.NewCategorizedError(ctx, apierror.InternalError, cgerror.FailedValidateVMSSParameters, cgerror.VMSS, err)
			log.GetLogger(ctx).Error(ctx, cerr.Error())
			return cerr
		}
		if vm.ProvisioningState != nil && strings.EqualFold(to.String(vm.ProvisioningState), "Deallocating") {
			log.GetLogger(ctx).Infof(ctx, "associateVMSSWithAgentPoolSLB: found VMSS %s vm %s being deleted or not valid, will ignore it", to.String(vmss.Name), to.String(vm.Name))
			continue
		}
		if vm.InstanceView == nil {
			newvm, err := c.vmssClient.GetVirtualMachineScaleSetVMWithInstanceViews(ctx, managedClusterSubscriptionID, resourceGroupName, to.String(vmss.Name), to.String(vm.InstanceID))
			if err != nil {
				log.GetLogger(ctx).Errorf(ctx, "associateVMSSWithAgentPoolSLB: failed to get the instance view of vmss %s, vm %s", to.String(vmss.Name), to.String(vm.Name))
				return err
			}
			vm = *newvm
		}
		if vm.InstanceView.Statuses != nil {
			if vmsshelper.DeallocatedAndStoppedVMFilter(ctx, vm) {
				log.GetLogger(ctx).Infof(ctx, "associateVMSSWithAgentPoolSLB: found VMSS %s vm %s is stopped or deallocated, will ignore it", to.String(vmss.Name), to.String(vm.Name))
				continue
			}
		}
		vmNics := *vm.NetworkProfileConfiguration.NetworkInterfaceConfigurations
		primaryVMNic, err := getPrimaryNetworkInterfaceConfiguration(vmNics)
		if err != nil {
			log.GetLogger(ctx).Errorf(ctx, "associateVMSSWithAgentPoolSLB: failed to get the primary IP config from the VMSS %s vm %s", to.String(vmss.Name), to.String(vm.Name))
			cerr := cgerror.NewCategorizedError(ctx, apierror.ClientError, cgerror.FailedValidateVMSSParameters, cgerror.VMSS, err)
			return cerr
		}
		primaryIPConfig, err := getPrimaryIPConfigFromVMSSNetworkConfig(primaryVMNic)
		if err != nil {
			log.GetLogger(ctx).Errorf(ctx, "associateVMSSWithAgentPoolSLB: failed to get the primary IP config from the VMSS %s", to.String(vmss.Name))
			cerr := cgerror.NewCategorizedError(ctx, apierror.ClientError, cgerror.FailedValidateVMSSParameters, cgerror.VMSS, err)
			return cerr
		}

		foundIPv4 := associateLoadBalancerOutBoundPools(ctx, to.String(vmss.Name), primaryIPConfig, backendpoolIDs)
		ipv6IPConfig := getIPv6ConfigFromVMSSNetworkConfig(primaryVMNic)
		foundIPv6 := associateLoadBalancerOutBoundPools(ctx, to.String(vmss.Name), ipv6IPConfig, backendpoolIDsIPV6)

		if !foundIPv4 && !foundIPv6 {
			log.GetLogger(ctx).Infof(ctx, "associateVMSSWithAgentPoolSLB: no need to update VMSS %s", to.String(vmss.Name))
			continue
		}

		if vm.ProvisioningState != nil {
			log.GetLogger(ctx).Infof(ctx, "associateVMSSWithAgentPoolSLB: update VMSS %s vm %s in %s state", to.String(vmss.Name), to.String(vm.Name), to.String(vm.ProvisioningState))
		}
		newVMParams := compute.VirtualMachineScaleSetVM{
			VirtualMachineScaleSetVMProperties: &compute.VirtualMachineScaleSetVMProperties{
				NetworkProfileConfiguration: &compute.VirtualMachineScaleSetVMNetworkProfileConfiguration{
					NetworkInterfaceConfigurations: &vmNics,
				},
			},
		}
		_, cerr = c.vmssClient.UpdateVirtualMachineScaleSetVM(ctx, managedClusterSubscriptionID, resourceGroupName, to.String(vmss.Name), to.String(vm.InstanceID), &newVMParams)
		if cerr != nil {
			if cerr.SubCode == cgerror.InvalidParameter && strings.Contains(cerr.OriginError.Error(), "is not an active Virtual Machine Scale Set VM") {
				log.GetLogger(ctx).Infof(ctx, "DecoupleLBBackendPoolFromVMSS: found VMSS %s vm %s is not active, will ignore it", to.String(vmss.Name), to.String(vm.Name))
				continue
			}
			log.GetLogger(ctx).Errorf(ctx, "associateVMSSWithAgentPoolSLB: failed to update VMSS instances: %s", cerr)
			return cerr
		}
	}
	return nil
}

// decoupleLoadBalancerPools decouples any load balancer backend pool IDs from the IP config on the VMSS if it exists.
// It will return true if there were pools removed from the IP config which indicates the VMSS must be updated, otherwise false.
func decoupleLoadBalancerBackendPools(ctx context.Context, sub string, resourceGroup string, vmssName string, vmssIPConfig *compute.VirtualMachineScaleSetIPConfiguration, backendpoolIDs, backendpoolIPv6IDs map[string]struct{}) (bool, *cgerror.CategorizedError) {
	// empty IPv6 is expected in single-stack IPv4 scenarios
	if vmssIPConfig == nil {
		log.GetLogger(ctx).Infof(ctx, "vmss %s has no IP config", vmssName)
		return false, nil
	}
	if vmssIPConfig.LoadBalancerBackendAddressPools == nil || len(*vmssIPConfig.LoadBalancerBackendAddressPools) == 0 {
		log.GetLogger(ctx).Infof(ctx, "IP config for %s has no LB backend address pools", vmssName)
		return false, nil
	}

	var changed bool
	var lbBackendPools []compute.SubResource
	for _, pool := range *vmssIPConfig.LoadBalancerBackendAddressPools {
		// decouple the vmss from the internal slbs as well, as the internal slbs are always unwanted
		if _, ok := backendpoolIDs[strings.ToLower(*pool.ID)]; ok {
			log.GetLogger(ctx).Infof(ctx, "DecoupleLBBackendPoolFromVMSS: found unwanted backend pool %s on VMSS %s", to.String(pool.ID), vmssName)
			continue
		}
		if _, ok := backendpoolIPv6IDs[strings.ToLower(*pool.ID)]; ok {
			log.GetLogger(ctx).Infof(ctx, "DecoupleLBBackendPoolFromVMSS: found unwanted backend pool %s on VMSS %s", to.String(pool.ID), vmssName)
			continue
		}
		lbBackendPools = append(lbBackendPools, pool)
	}

	if len(lbBackendPools) != len(*vmssIPConfig.LoadBalancerBackendAddressPools) {
		log.GetLogger(ctx).Infof(ctx, "backendpool found, updating IP config %s", to.String(vmssIPConfig.ID))
		vmssIPConfig.LoadBalancerBackendAddressPools = &lbBackendPools
		changed = true
	}
	return changed, nil
}

// associateLoadBalancerOutBoundPools associate any load balancer backend pool IDs with the IP config on the VMSS if it exists.
// It will return true if there were pools associated with the IP config which indicates the VMSS must be updated, otherwise false.
func associateLoadBalancerOutBoundPools(ctx context.Context, vmssName string, vmssIPConfig *compute.VirtualMachineScaleSetIPConfiguration, backendpoolIDs map[string]struct{}) bool {
	// empty IPv6 is expected in single-stack IPv4 scenarios
	if vmssIPConfig == nil {
		log.GetLogger(ctx).Infof(ctx, "vmss %s has no IP config", vmssName)
		return false
	}
	if vmssIPConfig.PublicIPAddressConfiguration != nil {
		log.GetLogger(ctx).Infof(ctx, "vmss %s has public IP config, can't add outbound rule to it", vmssName)
		return false
	}
	if vmssIPConfig.LoadBalancerBackendAddressPools == nil {
		log.GetLogger(ctx).Infof(ctx, "IP config for %s has no LB backend address pools", vmssName)
		vmssIPConfig.LoadBalancerBackendAddressPools = &[]compute.SubResource{}
	}

	var lbBackendPools []compute.SubResource
	for target := range backendpoolIDs {
		found := false
		for _, pool := range *vmssIPConfig.LoadBalancerBackendAddressPools {
			// decouple the vmss from the internal slbs as well, as the internal slbs are always unwanted
			if strings.EqualFold(target, *pool.ID) {
				log.GetLogger(ctx).Infof(ctx, "associateLoadBalancerOutBoundPools: found wanted backend pool %s on VMSS %s", to.String(pool.ID), vmssName)
				found = true
				break
			}
		}
		if !found {
			lbBackendPools = append(lbBackendPools, compute.SubResource{ID: to.StringPtr(target)})
		}
	}

	if len(lbBackendPools) > 0 {
		log.GetLogger(ctx).Infof(ctx, "backendpool not found, updating IP config %s", to.String(vmssIPConfig.ID))
		*vmssIPConfig.LoadBalancerBackendAddressPools = append(*vmssIPConfig.LoadBalancerBackendAddressPools, lbBackendPools...)
		return true
	}
	return false
}

func getIPv6ConfigFromVMSSNetworkConfig(vmssNetworkConfig *compute.VirtualMachineScaleSetNetworkConfiguration) *compute.VirtualMachineScaleSetIPConfiguration {
	if vmssNetworkConfig.IPConfigurations == nil {
		return nil
	}

	ipConfigurations := *vmssNetworkConfig.IPConfigurations
	for i, ipConfig := range ipConfigurations {
		if ipConfig.VirtualMachineScaleSetIPConfigurationProperties != nil && ipConfig.PrivateIPAddressVersion == compute.IPv6 {
			return &ipConfigurations[i]
		}
	}

	return nil
}

func getPrimaryIPConfigFromVMSSNetworkConfig(vmssNetworkConfig *compute.VirtualMachineScaleSetNetworkConfiguration) (*compute.VirtualMachineScaleSetIPConfiguration, error) {
	if vmssNetworkConfig.IPConfigurations == nil {
		return &compute.VirtualMachineScaleSetIPConfiguration{}, fmt.Errorf("vmssNetworkConfig.IPConfigurations is nil")
	}

	ipConfigurations := *vmssNetworkConfig.IPConfigurations
	if len(ipConfigurations) == 1 {
		return &ipConfigurations[0], nil
	}

	for idx := range ipConfigurations {
		ipConfig := &ipConfigurations[idx]
		if ipConfig.Primary != nil && *ipConfig.Primary {
			return ipConfig, nil
		}
	}

	return &compute.VirtualMachineScaleSetIPConfiguration{}, fmt.Errorf("failed to find a primary IP configuration")
}

func getPrimaryNetworkInterfaceConfiguration(networkConfigurations []compute.VirtualMachineScaleSetNetworkConfiguration) (*compute.VirtualMachineScaleSetNetworkConfiguration, error) {
	if len(networkConfigurations) == 1 {
		return &networkConfigurations[0], nil
	}

	for idx := range networkConfigurations {
		networkConfig := networkConfigurations[idx]
		if networkConfig.Primary != nil && *networkConfig.Primary {
			return &networkConfig, nil
		}
	}

	return &compute.VirtualMachineScaleSetNetworkConfiguration{}, fmt.Errorf("failed to find a primary network configuration")
}

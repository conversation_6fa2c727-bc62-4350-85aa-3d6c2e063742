package agentpool

import (
	"context"

	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
)

type AgentPoolLBBackendpoolReconciler interface {
	DecoupleLBBackendPool(tx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}) *cgerror.CategorizedError
	AssociateLBBackendpool(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError
}

package agentpool

import (
	"context"

	"go.goms.io/aks/rp/resourceprovider/sharedlib/consts"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/naming"
	basicUtilsStrings "go.goms.io/aks/rp/toolkit/basicutils/strings"
	"go.goms.io/aks/rp/toolkit/log"
)

// FilterBackendPoolsForExclusion filters backend pools based on whether the agent pool is excluded.
// Excluded agent pools should only join outbound pools, not inbound pools.
func FilterBackendPoolsForExclusion(ctx context.Context, backendpoolIDs map[string]struct{}, isExcludedAgentPool bool) map[string]struct{} {
	if !isExcludedAgentPool {
		// Non-excluded agent pools can join all pools
		return backendpoolIDs
	}

	// Excluded agent pools should only join outbound pools
	filteredPools := make(map[string]struct{})
	for poolID := range backendpoolIDs {
		if isOutboundBackendPool(poolID) {
			filteredPools[poolID] = struct{}{}
		} else {
			log.GetLogger(ctx).Infof(ctx, "Excluding inbound backend pool %s for excluded agent pool", poolID)
		}
	}
	return filteredPools
}

// isOutboundBackendPool checks if a backend pool ID corresponds to an outbound pool.
// Pool IDs are in format: /subscriptions/.../resourceGroups/.../providers/Microsoft.Network/loadBalancers/kubernetes(-internal)/backendAddressPools/{poolName}
func isOutboundBackendPool(poolID string) bool {
	// Use getResourceNameFromResourceID to extract the backend pool name and compare with constants
	backendPoolName := naming.GetResourceNameFromResourceID(poolID)
	return basicUtilsStrings.EqualFoldAnyString(
		backendPoolName, consts.SlbOutboundBackendPoolName, consts.SlbOutboundBackendPoolNameIPv6,
	)
}
